import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Form, Button, Row, Col, InputGroup, Alert, Badge } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faArrowLeft, faSave, faPlus, faTrash, faSearch, faUser, faPhone,
  faExclamationTriangle, faCheckCircle, faTimes, faIdCard, faEnvelope,
  faCalendarAlt, faCreditCard, faFlask, faCalculator, faSpinner,
  faFileInvoiceDollar
} from '@fortawesome/free-solid-svg-icons';
import { patientAPI } from '../../services/api';
import billingService from '../../services/billingAPI';
import { useAuth } from '../../context/AuthContext';
import { useTenant } from '../../context/TenantContext';
import '../../styles/BillingRegistrationCompact.css';

// Compact Patient Search Component
const PatientSearch = ({ onPatientSelect, searchTerm, setSearchTerm, searchResults, searching }) => {
  const [showResults, setShowResults] = useState(false);

  return (
    <div className="patient-search-compact">
      <InputGroup size="sm">
        <InputGroup.Text>
          <FontAwesomeIcon icon={faSearch} />
        </InputGroup.Text>
        <Form.Control
          type="text"
          placeholder="Search existing patient (name/phone)..."
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value);
            setShowResults(e.target.value.length > 0);
          }}
          onFocus={() => setShowResults(searchTerm.length > 0)}
        />
        {searchTerm && (
          <Button
            variant="outline-secondary"
            size="sm"
            onClick={() => {
              setSearchTerm('');
              setShowResults(false);
            }}
          >
            <FontAwesomeIcon icon={faTimes} />
          </Button>
        )}
      </InputGroup>
      
      {showResults && searchResults.length > 0 && (
        <div className="search-results-compact">
          {searchResults.slice(0, 5).map((patient) => (
            <div
              key={patient.id}
              className="search-result-item"
              onClick={() => {
                onPatientSelect(patient);
                setShowResults(false);
                setSearchTerm('');
              }}
            >
              <div className="patient-name">{patient.first_name} {patient.last_name}</div>
              <div className="patient-details">{patient.phone} • {patient.date_of_birth}</div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const BillingRegistrationCompact = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { tenantData, currentTenantContext } = useTenant();

  // Core states
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Patient search states
  const [patientSearchTerm, setPatientSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [searching, setSearching] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState(null);

  // Single form data state for both patient and billing
  const [formData, setFormData] = useState({
    // Registration Info
    sid: '', // Auto-generated
    date: new Date().toISOString().split('T')[0],
    branch: currentUser?.tenant_id || '',
    
    // Patient Info
    title: 'Mr.',
    firstName: '',
    lastName: '',
    dob: '',
    age: '',
    gender: 'Male',
    phone: '',
    email: '',
    address: '',
    
    // Medical Info
    referralSource: 'Doctor',
    clinicalRemarks: '',
    
    // Billing Info
    tests: [],
    subtotal: 0,
    discount: 0,
    discountRemarks: '',
    gst: 0,
    total: 0,
    paid: 0,
    balance: 0,
    paymentMode: 'Cash'
  });

  // Test management
  const [availableTests, setAvailableTests] = useState([]);
  const [testSearchTerm, setTestSearchTerm] = useState('');

  // Master data - simplified
  const titleOptions = ['Mr.', 'Mrs.', 'Ms.', 'Dr.', 'Baby', 'Master'];
  const genderOptions = ['Male', 'Female', 'Other'];
  const referralSources = ['Doctor', 'Hospital', 'Corporate', 'Lab', 'Insurance', 'Self'];
  const paymentModes = ['Cash', 'Card', 'UPI', 'Bank Transfer', 'Cheque'];

  // Load test profiles on mount
  useEffect(() => {
    loadTestProfiles();
    generateSID();
  }, []);

  const loadTestProfiles = async () => {
    try {
      // Mock test data - replace with actual API call
      setAvailableTests([
        { id: 1, name: 'Complete Blood Count (CBC)', price: 300 },
        { id: 2, name: 'Lipid Profile', price: 450 },
        { id: 3, name: 'Liver Function Test', price: 500 },
        { id: 4, name: 'Kidney Function Test', price: 400 },
        { id: 5, name: 'Thyroid Profile', price: 600 },
        { id: 6, name: 'Blood Sugar (Fasting)', price: 150 },
        { id: 7, name: 'HbA1c', price: 350 },
        { id: 8, name: 'Vitamin D', price: 800 },
        { id: 9, name: 'Vitamin B12', price: 700 },
        { id: 10, name: 'ESR', price: 100 }
      ]);
    } catch (error) {
      console.error('Error loading test profiles:', error);
    }
  };

  // Generate SID number
  const generateSID = () => {
    const timestamp = Date.now().toString().slice(-6);
    setFormData(prev => ({
      ...prev,
      sid: `SID${timestamp}`
    }));
  };

  // Patient search with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (patientSearchTerm.trim().length >= 2) {
        handlePatientSearch();
      } else {
        setSearchResults([]);
      }
    }, 300);
    return () => clearTimeout(timeoutId);
  }, [patientSearchTerm]);

  const handlePatientSearch = async () => {
    if (!patientSearchTerm.trim()) return;
    
    setSearching(true);
    try {
      const response = await patientAPI.searchPatients(patientSearchTerm);
      setSearchResults(response.data || []);
    } catch (error) {
      console.error('Error searching patients:', error);
      setSearchResults([]);
    } finally {
      setSearching(false);
    }
  };

  // Handle patient selection from search
  const handlePatientSelect = (patient) => {
    setSelectedPatient(patient);
    setFormData(prev => ({
      ...prev,
      title: patient.title || 'Mr.',
      firstName: patient.first_name || '',
      lastName: patient.last_name || '',
      dob: patient.date_of_birth || '',
      age: calculateAge(patient.date_of_birth) || '',
      gender: patient.gender || 'Male',
      phone: patient.phone || '',
      email: patient.email || '',
      address: patient.address || ''
    }));
    setPatientSearchTerm('');
  };

  // Calculate age from DOB
  const calculateAge = (dob) => {
    if (!dob) return '';
    const today = new Date();
    const birthDate = new Date(dob);
    const age = today.getFullYear() - birthDate.getFullYear();
    return age.toString();
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle test addition
  const handleAddTest = (test) => {
    const newTest = {
      id: test.id,
      name: test.name,
      price: test.price
    };
    
    setFormData(prev => ({
      ...prev,
      tests: [...prev.tests, newTest]
    }));
    
    calculateTotals();
  };

  // Handle test removal
  const handleRemoveTest = (testId) => {
    setFormData(prev => ({
      ...prev,
      tests: prev.tests.filter(test => test.id !== testId)
    }));
    
    calculateTotals();
  };

  // Calculate totals
  const calculateTotals = useCallback(() => {
    const subtotal = formData.tests.reduce((sum, test) => sum + test.price, 0);
    const gst = subtotal * 0.18; // 18% GST
    const total = subtotal + gst - formData.discount;
    const balance = total - formData.paid;

    setFormData(prev => ({
      ...prev,
      subtotal,
      gst,
      total,
      balance
    }));
  }, [formData.tests, formData.discount, formData.paid]);

  // Recalculate when tests, discount, or payment changes
  useEffect(() => {
    calculateTotals();
  }, [formData.tests, formData.discount, formData.paid, calculateTotals]);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (formData.tests.length === 0) {
      setError('Please add at least one test');
      return;
    }

    if (!formData.firstName.trim() || !formData.lastName.trim()) {
      setError('Patient name is required');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const billingData = {
        ...formData,
        patientName: `${formData.firstName} ${formData.lastName}`.trim(),
        isNewPatient: !selectedPatient
      };

      const response = await billingService.createBilling(billingData);
      
      if (response.success) {
        setSuccess('Billing registration completed successfully!');
        setTimeout(() => {
          navigate('/billing/reports');
        }, 2000);
      } else {
        setError(response.error || 'Failed to create billing record');
      }
    } catch (error) {
      console.error('Error submitting billing:', error);
      setError('Failed to submit billing. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Clear form
  const handleClear = () => {
    setFormData({
      sid: '',
      date: new Date().toISOString().split('T')[0],
      branch: currentUser?.tenant_id || '',
      title: 'Mr.',
      firstName: '',
      lastName: '',
      dob: '',
      age: '',
      gender: 'Male',
      phone: '',
      email: '',
      address: '',
      referralSource: 'Doctor',
      clinicalRemarks: '',
      tests: [],
      subtotal: 0,
      discount: 0,
      discountRemarks: '',
      gst: 0,
      total: 0,
      paid: 0,
      balance: 0,
      paymentMode: 'Cash'
    });
    setSelectedPatient(null);
    setPatientSearchTerm('');
    generateSID();
  };

  return (
    <div className="billing-registration-compact">
      {/* Header */}
      <div className="header-section">
        <div className="d-flex justify-content-between align-items-center">
          <div>
            <h4 className="mb-1">
              <FontAwesomeIcon icon={faFileInvoiceDollar} className="me-2" />
              Billing Registration
            </h4>
            <p className="text-muted mb-0 small">Single form for new & existing patients</p>
          </div>
          <div className="d-flex gap-2">
            <Button variant="outline-secondary" onClick={() => navigate('/billing/reports')}>
              <FontAwesomeIcon icon={faArrowLeft} className="me-1" />
              Back
            </Button>
            <Button variant="outline-warning" onClick={handleClear}>
              Clear Form
            </Button>
          </div>
        </div>
      </div>

      {/* Alerts */}
      {error && (
        <Alert variant="danger" className="mb-3">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert variant="success" className="mb-3">
          <FontAwesomeIcon icon={faCheckCircle} className="me-2" />
          {success}
        </Alert>
      )}

      {/* Patient Search */}
      <div className="search-section mb-3">
        <PatientSearch
          onPatientSelect={handlePatientSelect}
          searchTerm={patientSearchTerm}
          setSearchTerm={setPatientSearchTerm}
          searchResults={searchResults}
          searching={searching}
        />
        {selectedPatient && (
          <div className="selected-patient-info">
            <Badge bg="success">
              <FontAwesomeIcon icon={faUser} className="me-1" />
              Existing Patient: {selectedPatient.first_name} {selectedPatient.last_name}
            </Badge>
          </div>
        )}
      </div>

      {/* Main Form */}
      <Form onSubmit={handleSubmit}>
        <Row className="g-3">
          {/* Left Column - Patient Information */}
          <Col lg={6}>
            <div className="form-section">
              <h6 className="section-title">
                <FontAwesomeIcon icon={faUser} className="me-2" />
                Patient Information
              </h6>
              
              <Row className="g-2">
                <Col md={3}>
                  <Form.Group>
                    <Form.Label className="small">SID #</Form.Label>
                    <Form.Control
                      size="sm"
                      type="text"
                      name="sid"
                      value={formData.sid}
                      onChange={handleInputChange}
                      readOnly
                    />
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group>
                    <Form.Label className="small">Date</Form.Label>
                    <Form.Control
                      size="sm"
                      type="date"
                      name="date"
                      value={formData.date}
                      onChange={handleInputChange}
                      required
                    />
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group>
                    <Form.Label className="small">Title</Form.Label>
                    <Form.Select
                      size="sm"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                    >
                      {titleOptions.map(title => (
                        <option key={title} value={title}>{title}</option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group>
                    <Form.Label className="small">Gender</Form.Label>
                    <Form.Select
                      size="sm"
                      name="gender"
                      value={formData.gender}
                      onChange={handleInputChange}
                    >
                      {genderOptions.map(gender => (
                        <option key={gender} value={gender}>{gender}</option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>

              <Row className="g-2 mt-1">
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small">First Name *</Form.Label>
                    <Form.Control
                      size="sm"
                      type="text"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      required
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small">Last Name *</Form.Label>
                    <Form.Control
                      size="sm"
                      type="text"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      required
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row className="g-2 mt-1">
                <Col md={4}>
                  <Form.Group>
                    <Form.Label className="small">Date of Birth</Form.Label>
                    <Form.Control
                      size="sm"
                      type="date"
                      name="dob"
                      value={formData.dob}
                      onChange={handleInputChange}
                    />
                  </Form.Group>
                </Col>
                <Col md={2}>
                  <Form.Group>
                    <Form.Label className="small">Age</Form.Label>
                    <Form.Control
                      size="sm"
                      type="text"
                      name="age"
                      value={formData.age}
                      onChange={handleInputChange}
                      placeholder="25"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small">Phone *</Form.Label>
                    <Form.Control
                      size="sm"
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      required
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row className="g-2 mt-1">
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small">Email</Form.Label>
                    <Form.Control
                      size="sm"
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small">Referral Source</Form.Label>
                    <Form.Select
                      size="sm"
                      name="referralSource"
                      value={formData.referralSource}
                      onChange={handleInputChange}
                    >
                      {referralSources.map(source => (
                        <option key={source} value={source}>{source}</option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>

              <Form.Group className="mt-2">
                <Form.Label className="small">Address</Form.Label>
                <Form.Control
                  size="sm"
                  as="textarea"
                  rows={2}
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                />
              </Form.Group>

              <Form.Group className="mt-2">
                <Form.Label className="small">Clinical Remarks</Form.Label>
                <Form.Control
                  size="sm"
                  as="textarea"
                  rows={2}
                  name="clinicalRemarks"
                  value={formData.clinicalRemarks}
                  onChange={handleInputChange}
                />
              </Form.Group>
            </div>
          </Col>

          {/* Right Column - Billing Information */}
          <Col lg={6}>
            <div className="form-section">
              <h6 className="section-title">
                <FontAwesomeIcon icon={faCreditCard} className="me-2" />
                Billing Information
              </h6>

              {/* Test Selection */}
              <div className="test-selection mb-3">
                <Form.Label className="small">Add Tests</Form.Label>
                <InputGroup size="sm">
                  <Form.Control
                    type="text"
                    placeholder="Search tests..."
                    value={testSearchTerm}
                    onChange={(e) => setTestSearchTerm(e.target.value)}
                  />
                  <InputGroup.Text>
                    <FontAwesomeIcon icon={faFlask} />
                  </InputGroup.Text>
                </InputGroup>
                
                {testSearchTerm && (
                  <div className="test-dropdown">
                    {availableTests
                      .filter(test => test.name.toLowerCase().includes(testSearchTerm.toLowerCase()))
                      .slice(0, 5)
                      .map(test => (
                        <div
                          key={test.id}
                          className="test-item"
                          onClick={() => {
                            handleAddTest(test);
                            setTestSearchTerm('');
                          }}
                        >
                          <span>{test.name}</span>
                          <span className="test-price">₹{test.price}</span>
                        </div>
                      ))
                    }
                  </div>
                )}
              </div>

              {/* Selected Tests */}
              {formData.tests.length > 0 && (
                <div className="selected-tests mb-3">
                  <Form.Label className="small">Selected Tests</Form.Label>
                  <div className="tests-list">
                    {formData.tests.map(test => (
                      <div key={test.id} className="test-row">
                        <span className="test-name">{test.name}</span>
                        <span className="test-price">₹{test.price}</span>
                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={() => handleRemoveTest(test.id)}
                        >
                          <FontAwesomeIcon icon={faTrash} />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Billing Calculations */}
              <div className="billing-calculations">
                <Row className="g-2">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small">Subtotal</Form.Label>
                      <Form.Control
                        size="sm"
                        type="number"
                        value={formData.subtotal}
                        readOnly
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small">GST (18%)</Form.Label>
                      <Form.Control
                        size="sm"
                        type="number"
                        value={formData.gst.toFixed(2)}
                        readOnly
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Row className="g-2 mt-1">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small">Discount</Form.Label>
                      <Form.Control
                        size="sm"
                        type="number"
                        name="discount"
                        value={formData.discount}
                        onChange={handleInputChange}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small">Total Amount</Form.Label>
                      <Form.Control
                        size="sm"
                        type="number"
                        value={formData.total.toFixed(2)}
                        readOnly
                        className="fw-bold"
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Row className="g-2 mt-1">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small">Amount Paid</Form.Label>
                      <Form.Control
                        size="sm"
                        type="number"
                        name="paid"
                        value={formData.paid}
                        onChange={handleInputChange}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small">Balance</Form.Label>
                      <Form.Control
                        size="sm"
                        type="number"
                        value={formData.balance.toFixed(2)}
                        readOnly
                        className={formData.balance > 0 ? 'text-danger fw-bold' : 'text-success fw-bold'}
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Form.Group className="mt-2">
                  <Form.Label className="small">Payment Mode</Form.Label>
                  <Form.Select
                    size="sm"
                    name="paymentMode"
                    value={formData.paymentMode}
                    onChange={handleInputChange}
                  >
                    {paymentModes.map(mode => (
                      <option key={mode} value={mode}>{mode}</option>
                    ))}
                  </Form.Select>
                </Form.Group>

                {formData.discount > 0 && (
                  <Form.Group className="mt-2">
                    <Form.Label className="small">Discount Remarks</Form.Label>
                    <Form.Control
                      size="sm"
                      type="text"
                      name="discountRemarks"
                      value={formData.discountRemarks}
                      onChange={handleInputChange}
                      placeholder="Reason for discount"
                    />
                  </Form.Group>
                )}
              </div>
            </div>
          </Col>
        </Row>

        {/* Submit Button */}
        <div className="submit-section mt-4">
          <div className="d-flex justify-content-end gap-2">
            <Button
              variant="success"
              type="submit"
              disabled={loading || formData.tests.length === 0}
              className="px-4"
            >
              <FontAwesomeIcon icon={loading ? faSpinner : faSave} spin={loading} className="me-2" />
              {loading ? 'Saving...' : 'Save Billing'}
            </Button>
          </div>
        </div>
      </Form>
    </div>
  );
};

export default BillingRegistrationCompact;
